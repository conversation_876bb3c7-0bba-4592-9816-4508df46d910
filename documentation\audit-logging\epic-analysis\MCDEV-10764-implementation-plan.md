# MCDEV-10872 Implementation Plan: Create Audit Log for Event Setup

## Epic Overview
**Epic ID**: MCDEV-10872  
**Title**: Create Audit Log for Event Setup  
**Business Rationale**: Support has benefited greatly from having Subscription Setup audit logs as proof of who made a change and when. Audit logs for Event setup would be similarly beneficial.

## Requirements Analysis

### Business Requirements
- **Support Troubleshooting**: Provide detailed change history for event setup issues
- **Compliance Tracking**: Maintain audit trails for event configuration changes
- **Administrative Oversight**: Enable monitoring of event setup and maintenance activities
- **Context-Aware Logging**: Some changes tied to site alone, others tied to specific eventID
- **Future Integration**: Option to display audit log entries on the event itself

### Technical Requirements
- **Scope Control**: Both site-level and event-specific audit logging
- **Area Categorization**: Separate into functional areas like Subscription setup
- **Admin Interface**: Audit Log admin tool under Events \ Settings
- **Pattern Consistency**: Follow MCDEV-9333 (Subscription Setup) implementation patterns
- **Performance**: Minimal impact on existing event operations

## Implementation Strategy

### Recommended Approach: Area-Categorized with Event Context
**Collection Strategy**: `auditLog_EV` (specialized collection)  
**AUDITCODE**: "EV" (Events)  
**Pattern**: Area-Categorized Implementation (Approach B) with context enhancement

### MongoDB Collection Schema
```javascript
{
    "ORGID": NumberInt,
    "SITEID": NumberInt,
    "AUDITCODE": "EV",
    "AREACODE": String,      // Functional area categorization
    "EVENTID": NumberInt,    // Event-specific context (0 for site-level)
    "ACTORMEMBERID": NumberInt,
    "ACTIONDATE": ISODate,
    "MESSAGE": String
}
```

### Area Codes (AREACODE Values)
| Code | Description | Scope |
|------|-------------|-------|
| DETAILS | Event basic information (title, dates, status) | Event-specific |
| REGISTRATION | Registration setup and configuration | Event-specific |
| RATES | Rate schedules and pricing | Event-specific |
| FORMS | Event forms and evaluations | Event-specific |
| DOCUMENTS | Event documents and materials | Event-specific |
| SUBEVENTS | Sub-event management | Event-specific |
| TICKETS | Ticket packages and configurations | Event-specific |
| CREDITS | Credit assignments | Event-specific |
| CUSTOM | Custom fields | Event-specific |
| SETTINGS | Event-level settings and calendar associations | Mixed |

## Child Stories Breakdown

### Story 1: Core Event Audit Infrastructure
**Story ID**: MCDEV-10872-1  
**Title**: Create Core Event Audit Logging Infrastructure  
**Effort Estimate**: 2-3 hours

**Description**:
Establish the foundational audit logging infrastructure for event setup operations, including the specialized MongoDB collection and core stored procedure.

**Technical Implementation**:
- Create `ev_insertAuditLog` stored procedure following `sub_insertAuditLog` pattern
- Create `auditLog_EV.cfc` MongoDB model with AREACODE and EVENTID properties
- Implement basic error handling and message sanitization
- Set up queue-based processing integration

**Acceptance Criteria**:
-  `ev_insertAuditLog` procedure created and tested
-  `auditLog_EV` MongoDB model created with proper schema
-  Basic audit log insertion works for test scenarios
-  Error handling follows established patterns
-  Message sanitization using `fn_cleanInvalidXMLChars()`

**Dependencies**: None

### Story 2: Event Details Audit Logging
**Story ID**: MCDEV-10872-2  
**Title**: Add Audit Logging to Event Details Operations  
**Effort Estimate**: 1.5-2 hours

**Description**:
Implement audit logging for core event CRUD operations including event creation, updates, and deletion.

**Technical Implementation**:
- Modify `ev_createEvent` procedure to log event creation
- Modify `ev_updateEvent` procedure to log event updates with change detection
- Modify `ev_deleteEvent` procedure to log event deletion
- Use AREACODE "DETAILS" for all operations
- Include event name, code, and key details in messages

**Message Format Examples**:
- `Event [EventName] has been created with code [EventCode].`
- `Event [EventName] updated. The following changes have been made: [ChangeDetails]`
- `Event [EventName] has been deleted.`

**Acceptance Criteria**:
-  Event creation generates audit log entry
-  Event updates generate detailed change descriptions
-  Event deletion generates audit log entry
-  All messages include event context and actor information
-  EVENTID field populated correctly

**Dependencies**: Story 1

### Story 3: Event Registration Setup Audit Logging
**Story ID**: MCDEV-10872-3  
**Title**: Add Audit Logging to Event Registration Operations  
**Effort Estimate**: 1.5-2 hours

**Description**:
Implement audit logging for event registration setup including registration creation, modification, and configuration changes.

**Technical Implementation**:
- Identify registration-related stored procedures
- Add audit logging to registration creation/modification procedures
- Track registration type changes, confirmation settings, capacity limits
- Use AREACODE "REGISTRATION"
- Implement before/after change detection for complex updates

**Message Format Examples**:
- `Registration setup created for event [EventName].`
- `Registration settings updated for event [EventName]. Changes: [ChangeDetails]`
- `Registration type changed from [OldType] to [NewType] for event [EventName].`

**Acceptance Criteria**:
-  Registration creation generates audit log entry
-  Registration modifications tracked with detailed changes
-  Registration type changes specifically noted
-  Confirmation and capacity setting changes tracked
-  Messages provide clear context about what changed

**Dependencies**: Story 1

### Story 4: Event Rates Audit Logging
**Story ID**: MCDEV-10872-4  
**Title**: Add Audit Logging to Event Rates Operations  
**Effort Estimate**: 1-1.5 hours

**Description**:
Implement audit logging for event rate management including rate creation, modification, and deletion.

**Technical Implementation**:
- Identify rate-related stored procedures
- Add audit logging to rate CRUD operations
- Track rate schedule changes and pricing modifications
- Use AREACODE "RATES"
- Include rate names, amounts, and effective dates in messages

**Message Format Examples**:
- `Rate [RateName] created for event [EventName] with amount $[Amount].`
- `Rate [RateName] updated for event [EventName]. Changes: [ChangeDetails]`
- `Rate [RateName] deleted from event [EventName].`

**Acceptance Criteria**:
-  Rate creation generates audit log entry
-  Rate modifications tracked with pricing changes
-  Rate deletion generates audit log entry
-  Rate schedule changes tracked
-  Messages include rate amounts and effective dates

**Dependencies**: Story 1

### Story 5: Event Forms Audit Logging
**Story ID**: MCDEV-10872-5  
**Title**: Add Audit Logging to Event Forms Operations  
**Effort Estimate**: 1-1.5 hours

**Description**:
Implement audit logging for event forms and evaluations including form associations and configuration changes.

**Technical Implementation**:
- Modify `ev_addEventForm` procedure to log form associations
- Modify `ev_updateEventForm` procedure to log form configuration changes
- Track form load points, response limits, and question configurations
- Use AREACODE "FORMS"
- Include form names and configuration details in messages

**Message Format Examples**:
- `Form [FormName] associated with event [EventName] at load point [LoadPoint].`
- `Form configuration updated for [FormName] on event [EventName]. Changes: [ChangeDetails]`
- `Form [FormName] removed from event [EventName].`

**Acceptance Criteria**:
-  Form associations generate audit log entries
-  Form configuration changes tracked
-  Load point changes specifically noted
-  Response limit changes tracked
-  Form removal generates audit log entry

**Dependencies**: Story 1

### Story 6: Event Documents and Materials Audit Logging
**Story ID**: MCDEV-10872-6  
**Title**: Add Audit Logging to Event Documents Operations  
**Effort Estimate**: 1-1.5 hours

**Description**:
Implement audit logging for event documents and materials including uploads, modifications, and deletions.

**Technical Implementation**:
- Identify document-related stored procedures
- Add audit logging to document upload/modification procedures
- Track document associations and content changes
- Use AREACODE "DOCUMENTS"
- Include document names and types in messages

**Message Format Examples**:
- `Document [DocumentName] uploaded to event [EventName].`
- `Document [DocumentName] updated for event [EventName].`
- `Document [DocumentName] removed from event [EventName].`

**Acceptance Criteria**:
-  Document uploads generate audit log entries
-  Document modifications tracked
-  Document deletions generate audit log entries
-  Document type and size information included
-  Messages provide clear document context

**Dependencies**: Story 1

### Story 7: Sub-Events Audit Logging
**Story ID**: MCDEV-10872-7
**Title**: Add Audit Logging to Sub-Events Operations
**Effort Estimate**: 1-1.5 hours

**Description**:
Implement audit logging for sub-event management including creation, linking, unlinking, and settings configuration.

**Technical Implementation**:
- Add audit logging to sub-event creation procedures
- Track sub-event linking and unlinking operations
- Monitor sub-event settings and configuration changes
- Use AREACODE "SUBEVENTS"
- Include parent-child event relationships in messages

**Message Format Examples**:
- `Sub-event [SubEventName] created under parent event [ParentEventName].`
- `Existing event [EventName] linked as sub-event to [ParentEventName].`
- `Sub-event [SubEventName] unlinked from parent event [ParentEventName].`

**Acceptance Criteria**:
-  Sub-event creation generates audit log entries
-  Event linking/unlinking tracked
-  Sub-event settings changes monitored
-  Parent-child relationships clearly indicated
-  Force selection settings tracked

**Dependencies**: Story 1

### Story 8: Event Tickets and Packages Audit Logging
**Story ID**: MCDEV-10872-8
**Title**: Add Audit Logging to Event Tickets Operations
**Effort Estimate**: 1-1.5 hours

**Description**:
Implement audit logging for ticket packages and configurations including creation, modification, and deletion.

**Technical Implementation**:
- Identify ticket-related stored procedures
- Add audit logging to ticket package CRUD operations
- Track ticket configurations and pricing changes
- Use AREACODE "TICKETS"
- Include ticket package names and pricing in messages

**Message Format Examples**:
- `Ticket package [PackageName] created for event [EventName] with price $[Price].`
- `Ticket package [PackageName] updated for event [EventName]. Changes: [ChangeDetails]`
- `Ticket package [PackageName] deleted from event [EventName].`

**Acceptance Criteria**:
-  Ticket package creation generates audit log entries
-  Ticket modifications tracked with pricing changes
-  Ticket deletion generates audit log entries
-  Package configurations tracked
-  Messages include pricing and availability details

**Dependencies**: Story 1

### Story 9: Event Credits Audit Logging
**Story ID**: MCDEV-10872-9
**Title**: Add Audit Logging to Event Credits Operations
**Effort Estimate**: 1-1.5 hours

**Description**:
Implement audit logging for credit assignments and modifications including credit type changes and allocations.

**Technical Implementation**:
- Identify credit-related stored procedures
- Add audit logging to credit assignment operations
- Track credit type changes and hour allocations
- Use AREACODE "CREDITS"
- Include credit types and amounts in messages

**Message Format Examples**:
- `Credits assigned to event [EventName]: [CreditDetails].`
- `Credit allocation updated for event [EventName]. Changes: [ChangeDetails]`
- `Credits removed from event [EventName].`

**Acceptance Criteria**:
-  Credit assignments generate audit log entries
-  Credit modifications tracked with detailed changes
-  Credit removals generate audit log entries
-  Credit types and hours clearly specified
-  Messages provide clear credit context

**Dependencies**: Story 1

### Story 10: Event Custom Fields Audit Logging
**Story ID**: MCDEV-10872-10
**Title**: Integrate Event Custom Fields with Audit Logging
**Effort Estimate**: 1 hour

**Description**:
Integrate event-specific custom field changes with the existing custom field audit logging system.

**Technical Implementation**:
- Modify existing custom field audit procedures for event context
- Ensure event-specific custom field changes use AREACODE "CUSTOM"
- Add EVENTID context to custom field audit messages
- Integrate with existing MEMCF audit code patterns

**Message Format Examples**:
- `Custom field [FieldName] added to event [EventName].`
- `Custom field [FieldName] updated for event [EventName]. Value changed from [OldValue] to [NewValue].`
- `Custom field [FieldName] removed from event [EventName].`

**Acceptance Criteria**:
-  Event custom field changes generate audit log entries
-  Integration with existing custom field audit system
-  EVENTID context properly populated
-  Field value changes tracked
-  Messages provide clear field context

**Dependencies**: Story 1

### Story 11: Event Settings Audit Logging
**Story ID**: MCDEV-10872-11
**Title**: Add Audit Logging to Event Settings Operations
**Effort Estimate**: 1 hour

**Description**:
Implement audit logging for event-level settings changes including calendar associations and category changes.

**Technical Implementation**:
- Add audit logging to event settings procedures
- Track calendar association changes
- Monitor category assignments and modifications
- Use AREACODE "SETTINGS"
- Include setting names and values in messages

**Message Format Examples**:
- `Event [EventName] moved from calendar [OldCalendar] to [NewCalendar].`
- `Category [CategoryName] assigned to event [EventName].`
- `Event settings updated for [EventName]. Changes: [ChangeDetails]`

**Acceptance Criteria**:
-  Calendar changes generate audit log entries
-  Category assignments tracked
-  Event settings modifications monitored
-  Messages provide clear settings context
-  Both site-level and event-specific settings tracked

**Dependencies**: Story 1

### Story 12: Event Audit Log Admin Interface
**Story ID**: MCDEV-10872-12
**Title**: Create Event Audit Log Admin Interface
**Effort Estimate**: 2-3 hours

**Description**:
Create comprehensive admin interface for viewing event audit logs with filtering and search capabilities.

**Technical Implementation**:
- Create admin navigation under Events \ Settings \ Audit Log
- Implement `dsp_eventAuditLog.cfm` with DataTables interface
- Add `getEventAuditLogs` method to EventAdmin.cfc
- Implement filtering by date range, area, event, and keywords
- Add event-specific audit log view option

**Interface Features**:
- Date range filtering with date pickers
- Area dropdown filtering (All Areas, DETAILS, REGISTRATION, etc.)
- Event selection dropdown for event-specific filtering
- Keyword search functionality
- Clear filters functionality
- Responsive design with Bootstrap styling
- Server-side processing for performance

**Acceptance Criteria**:
-  Admin navigation created under Events \ Settings
-  Audit log interface displays all event audit entries
-  Filtering works for all implemented filters
-  Event-specific view shows only selected event's changes
-  Interface follows established MemberCentral patterns
-  Performance acceptable with large datasets

**Dependencies**: Stories 1-11

## Questions for Clarification

### 1. Event-Specific Display Integration
The requirement mentions "giving us the option of displaying audit log entries on the event itself at some point." Should this be:
- A new tab on the event detail page?
- A section within existing tabs?
- A separate modal/popup accessible from event details?

### 2. Site vs Event Scope Clarification
Which operations should be site-only vs event-specific?
- **Site-only suggestions**: Event settings, calendar configurations, global event policies
- **Event-specific suggestions**: Event details, registration, rates, forms, documents

### 3. Area Granularity Level
Should we track at a more granular level within areas? For example:
- **REGISTRATION**: Registration type, confirmation settings, capacity limits as separate sub-areas?
- **RATES**: Individual rate changes vs rate schedule modifications?

### 4. Historical Data Requirements
- Do we need to audit log existing events retroactively?
- Should we only track changes going forward from implementation date?

### 5. Performance Considerations
- Are there specific performance requirements given high event operation volume?
- Should we implement any throttling or batching for high-frequency operations?

### 6. Integration with Existing Activity Logging
- Should this replace the existing `platformstatsMC.dbo.act_recordLog` calls in event procedures?
- Should both systems run in parallel?
- How should we handle the transition?

## Implementation Timeline

### Phase 1: Foundation (Week 1)
- **Stories 1-2**: Core infrastructure and event details audit logging
- **Deliverables**: Basic audit logging framework operational
- **Risk**: Low - established patterns

### Phase 2: Core Operations (Week 2)
- **Stories 3-5**: Registration, rates, and forms audit logging
- **Deliverables**: Most common event operations tracked
- **Risk**: Low - straightforward implementations

### Phase 3: Extended Features (Week 3)
- **Stories 6-9**: Documents, sub-events, tickets, credits audit logging
- **Deliverables**: Comprehensive event operation coverage
- **Risk**: Medium - less common operations may need custom logic

### Phase 4: Integration (Week 4)
- **Stories 10-12**: Custom fields integration and admin interface
- **Deliverables**: Complete user-facing audit logging system
- **Risk**: Medium - user experience and integration considerations

## Success Criteria

### Functional Requirements
-  All event CRUD operations have appropriate audit logging
-  Admin interface provides comprehensive filtering and search
-  Event-specific audit log viewing capability
-  Support team can easily track event changes for troubleshooting

### Performance Requirements
-  Performance impact < 5% on event operations
-  Admin interface loads within 2 seconds for 1000+ records
-  Audit log insertion < 50ms per operation

### Quality Requirements
-  Consistent message formatting across all areas
-  Proper error handling and graceful degradation
-  User-friendly interface following MemberCentral patterns
-  Comprehensive test coverage for all audit scenarios

**Total Estimated Effort**: 15-20 hours across 12 stories
**Recommended Timeline**: 4 weeks with proper testing and validation
**Business Value**: Significant improvement in support troubleshooting capabilities
