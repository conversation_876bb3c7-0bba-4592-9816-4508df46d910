# MCDEV-10869 Implementation Summary: Create Core Event Audit Logging Infrastructure

## Overview
**Story ID**: MCDEV-10869  
**Title**: Create Core Event Audit Logging Infrastructure  
**Status**: Completed  
**Implementation Date**: September 2025

## Business Value
Establishes the foundational audit logging infrastructure for event setup operations, enabling comprehensive tracking of event configuration changes for support troubleshooting, compliance, and administrative oversight.

## Technical Implementation

### 1. Core Stored Procedure: `ev_insertAuditLog`
**Location**: `database/membercentral/schema/memberCentral/procedures/ev_insertAuditLog.sql`

**Parameters**:
- `@orgID int` - Organization ID
- `@siteID int` - Site ID  
- `@areaCode varchar(100)` - Functional area code (DETAILS, REGISTRATION, RATES, etc.)
- `@eventID int` - Event ID (0 for site-level operations)
- `@msgjson varchar(max)` - Audit message content
- `@isImport bit = 0` - Import operation flag (adds [Import] prefix)
- `@enteredByMemberID int` - Actor member ID

**Key Features**:
- Follows established `sub_insertAuditLog` pattern
- Uses queue-based MongoDB processing via `platformQueue.dbo.queue_mongo`
- Implements proper error handling with `up_MCErrorHandler`
- Sanitizes messages using `fn_cleanInvalidXMLChars()`
- Supports both event-specific and site-level audit logging

### 2. MongoDB Model: `auditLog_EV.cfc`
**Location**: `models/system/platform/mongo/auditLog_EV.cfc`

**Schema Properties**:
- `ORGID` - Organization ID (numeric)
- `SITEID` - Site ID (numeric)
- `AUDITCODE` - Always "EV" for events (string)
- `AREACODE` - Functional area categorization (string)
- `EVENTID` - Event context ID, 0 for site-level (numeric)
- `ACTORMEMBERID` - Actor member ID (numeric)
- `ACTIONDATE` - Action timestamp (date)
- `MESSAGE` - Audit message content (string)

**Collection**: `AuditLog_EV` in `membercentralAudit` database

### 3. Database Migration
**Location**: `database/membercentral/migrations/2025/2025-09/MCDEV-10869 - Create Core Event Audit Logging Infrastructure.sql`

**Contents**:
- Creates `ev_insertAuditLog` stored procedure
- Includes comprehensive documentation
- Contains basic functionality test
- Follows established migration patterns

## Area Codes (AREACODE Values)
Based on the implementation plan, the following area codes will be used:

| Code | Description | Scope |
|------|-------------|-------|
| DETAILS | Event basic information (title, dates, status) | Event-specific |
| REGISTRATION | Registration setup and configuration | Event-specific |
| RATES | Rate schedules and pricing | Event-specific |
| FORMS | Event forms and evaluations | Event-specific |
| DOCUMENTS | Event documents and materials | Event-specific |
| SUBEVENTS | Sub-event management | Event-specific |
| TICKETS | Ticket packages and configurations | Event-specific |
| CREDITS | Credit assignments | Event-specific |
| CUSTOM | Custom fields | Event-specific |
| SETTINGS | Event-level settings and calendar associations | Mixed |

## Usage Examples

### Event-Specific Audit Log
```sql
EXEC dbo.ev_insertAuditLog 
    @orgID = 1,
    @siteID = 1,
    @areaCode = 'DETAILS',
    @eventID = 12345,
    @msgjson = 'Event [Annual Conference] has been created with code [CONF-2025].',
    @isImport = 0,
    @enteredByMemberID = 1001;
```

### Site-Level Audit Log
```sql
EXEC dbo.ev_insertAuditLog 
    @orgID = 1,
    @siteID = 1,
    @areaCode = 'SETTINGS',
    @eventID = 0,
    @msgjson = 'Event calendar settings updated for site.',
    @isImport = 0,
    @enteredByMemberID = 1001;
```

### Import Operation
```sql
EXEC dbo.ev_insertAuditLog 
    @orgID = 1,
    @siteID = 1,
    @areaCode = 'REGISTRATION',
    @eventID = 12346,
    @msgjson = 'Registration setup created for event [Imported Event].',
    @isImport = 1,
    @enteredByMemberID = 1001;
```

## Testing
**Test Script**: `tests/event-audit-logging-test.sql`

**Test Coverage**:
- Basic audit log insertion
- Import operations with prefix
- Site-level operations (eventID = 0)
- Message sanitization with special characters
- Multiple area codes functionality

## Integration Points

### Queue Processing
- Audit entries are inserted into `platformQueue.dbo.queue_mongo`
- MongoDB processing moves entries to `AuditLog_EV` collection
- Follows established queue-based architecture

### Error Handling
- Uses `SET XACT_ABORT, NOCOUNT ON` pattern
- Implements TRY/CATCH with `up_MCErrorHandler`
- Returns 0 for success, -1 for errors

### Message Sanitization
- Uses `fn_cleanInvalidXMLChars()` for message cleaning
- Escapes double quotes for JSON compatibility
- Handles special characters safely

## Next Steps
This infrastructure enables the implementation of the remaining 11 child stories:

1. **MCDEV-10764-2**: Event Details Audit Logging
2. **MCDEV-10764-3**: Event Registration Setup Audit Logging
3. **MCDEV-10764-4**: Event Rates Audit Logging
4. **MCDEV-10764-5**: Event Forms Audit Logging
5. **MCDEV-10764-6**: Event Documents Audit Logging
6. **MCDEV-10764-7**: Sub-Events Audit Logging
7. **MCDEV-10764-8**: Event Tickets Audit Logging
8. **MCDEV-10764-9**: Event Credits Audit Logging
9. **MCDEV-10764-10**: Event Custom Fields Integration
10. **MCDEV-10764-11**: Event Settings Audit Logging
11. **MCDEV-10764-12**: Event Audit Log Admin Interface

## Acceptance Criteria Status
- [x] `ev_insertAuditLog` procedure created and tested
- [x] `auditLog_EV` MongoDB model created with proper schema
- [x] Basic audit log insertion works for test scenarios
- [x] Error handling follows established patterns
- [x] Message sanitization using `fn_cleanInvalidXMLChars()`

## Files Created/Modified
1. `database/membercentral/schema/memberCentral/procedures/ev_insertAuditLog.sql` - Core stored procedure
2. `models/system/platform/mongo/auditLog_EV.cfc` - MongoDB model
3. `database/membercentral/migrations/2025/2025-09/MCDEV-10869 - Create Core Event Audit Logging Infrastructure.sql` - Migration script
4. `tests/event-audit-logging-test.sql` - Test script
5. `documentation/audit-logging/MCDEV-10869-implementation-summary.md` - This documentation

**Implementation Complete**: The core event audit logging infrastructure is ready for use by subsequent child stories.
