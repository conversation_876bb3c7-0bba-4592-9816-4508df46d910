USE membercentral
GO

CREATE PROCEDURE dbo.ev_insertAuditLog
@orgID int,
@siteID int,
@areaCode varchar(100),
@eventID int,
@msgjson varchar(max),
@isImport bit = 0,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	-- Add import prefix if this is an import operation
	IF @isImport = 1
		SET @msgjson = '[Import] ' + @msgjson;

	-- Insert audit log entry into MongoDB queue
	-- Uses auditLog_EV collection with EVENTID context field
	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog_EV", "d": {
		"AUDITCODE":"EV",
		"AREACODE":"' + @areaCode + '",
		"EVENTID":' + cast(@eventID as varchar(10)) + ',
		"ORGID":' + cast(@orgID as varchar(10)) + ',
		"SITEID":' + cast(@siteID as varchar(10)) + ',
		"ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
		"ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
		"MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

