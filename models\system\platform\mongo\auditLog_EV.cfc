component name="auditLog_EV" extends="cbmongodb.models.ActiveEntity" collection="AuditLog" database="membercentralAudit" accessors=false {
	property name="ORGID" schema=true validate="numeric";
	property name="SITEID" schema=true validate="numeric";
	property name="AUDITCODE" schema=true validate="string";
	property name="AREACODE" schema=true validate="string";
	property name="EVENTID" schema=true validate="numeric";
	property name="ACTORMEMBERID" schema=true validate="numeric";
	property name="ACTIONDATE" schema=true validate="date";
	property name="MESSAGE" schema=true validate="string";
}
