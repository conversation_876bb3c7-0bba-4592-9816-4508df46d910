<cfcomponent output="false">

	<cffunction name="getAppBaseLink" access="public" returntype="string" output="false">
		<cfargument name="applicationInstanceID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qPagePath" cachedwithin="#createTimeSpan(0,0,0,15)#">
			select * from
			dbo.fn_cms_getApplicationInstancePagePath(<cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.applicationInstanceID#" cfsqltype="CF_SQL_INTEGER">)
			order by level desc
		</cfquery>
		<cfset local.pageLink = "">
		<cfset local.communityFound = false>
		<cfset local.socialNetworkFound = false>
		<cfset local.previousLevelAppType = "">
		<cfset local.previousLevelPageValuePair = "">
		<cfset local.currentPgUrlVar = "">

		<cfloop query="local.qPagePath">

			<cfswitch expression="#local.previousLevelAppType#">
				<cfcase value="SocialNetwork">
					<cfset local.currentPgUrlVar = "snpg">
				</cfcase>
				<cfcase value="Community">
					<cfset local.currentPgUrlVar = "commpg">
				</cfcase>
				<cfdefaultcase>
					<cfset local.currentPgUrlVar = "pg">
				</cfdefaultcase>
			</cfswitch>
			
			<cfswitch expression="#local.qPagePath.applicationSiteResourceType#">
				<cfcase value="SocialNetwork">
					<cfset local.socialNetworkFound = true>
					<cfset local.previousLevelPageValuePair = "#local.currentPgUrlVar#=#local.qPagePath.localSocialNetworkPageName#">
					<cfset local.pageLink = listappend(local.pageLink,local.previousLevelPageValuePair,"&")>
				</cfcase>
				<cfcase value="Community">
					<cfset local.communityFound = true>
					<cfswitch expression="#local.previousLevelAppType#">
						<cfcase value="SocialNetwork">
							<cfset local.previousLevelPageValuePair = "#local.currentPgUrlVar#=#local.qPagePath.snAlias#">
						</cfcase>
						<cfdefaultcase>
							<cfset local.previousLevelPageValuePair = "#local.currentPgUrlVar#=#local.qPagePath.pageName#">
						</cfdefaultcase>
					</cfswitch>
					<cfif local.qPagePath.currentRow eq local.qPagePath.recordcount>
						<cfset local.pageLink = listappend(local.pageLink,local.previousLevelPageValuePair,"&")>
					</cfif>
				</cfcase>
				<cfdefaultcase>
					<cfif local.communityFound and local.previousLevelAppType eq "Community">
						<cfset local.pageLink = listappend(local.pageLink,local.previousLevelPageValuePair,"&")>
					</cfif>
					<cfswitch expression="#local.previousLevelAppType#">
						<cfcase value="SocialNetwork">
							<cfset local.pageLink = listappend(local.pageLink,"#local.currentPgUrlVar#=#local.qPagePath.snAlias#","&")>
						</cfcase>
						<cfdefaultcase>
							<cfset local.pageLink = listappend(local.pageLink,"#local.currentPgUrlVar#=#local.qPagePath.pageName#","&")>
						</cfdefaultcase>
					</cfswitch>
				</cfdefaultcase>
			</cfswitch>
			<cfset local.previousLevelAppType = local.qPagePath.applicationSiteResourceType>
		</cfloop>
		<cfreturn local.pageLink>
	</cffunction>

	<cffunction name="getAppLocation" access="public" returntype="struct" output="false">
		<cfargument name="applicationInstanceID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data" cachedwithin="#createTimeSpan(0,0,15,0)#">
			select * from
			dbo.fn_cms_getApplicationInstancePagePath(<cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.applicationInstanceID#" cfsqltype="CF_SQL_INTEGER">)
			order by level desc
		</cfquery>
		
		<cfscript>
			local.returnStruct 										= structNew();
			local.returnStruct.isInCommunity 			= FALSE;
			local.returnStruct.isInSocialNetwork 	= FALSE;
			local.returnStruct.commAppInstanceID 	= '';
			local.returnStruct.commSiteResourceID = '';
			local.returnStruct.snAppInstanceID 		= '';
			local.returnStruct.snSiteResourceID 	= '';
		</cfscript>

		<cfloop query="local.data">
			<cfswitch expression="#local.data.applicationSiteResourceType#">
				
				<cfcase value="SocialNetwork">
					<cfset local.returnStruct.isInSocialNetwork = true />
					<cfset local.returnStruct.snAppInstanceID 		= local.data.applicationInstanceID />
					<cfset local.returnStruct.snSiteResourceID 		= local.data.applicationSiteResourceID />
				</cfcase>
				
				<cfcase value="Community">
					<cfset local.returnStruct.isInCommunity = true />
					<cfset local.returnStruct.commAppInstanceID 	= local.data.applicationInstanceID />
					<cfset local.returnStruct.commSiteResourceID	= local.data.applicationSiteResourceID />
				</cfcase>
			</cfswitch>
		</cfloop>
		
		<cfreturn local.returnStruct />
	</cffunction>
	<cffunction name="getInstanceSettings" access="public" returntype="struct">
		<cfargument name="appInstanceID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.strInstanceSettings=structNew()>
		
		<cfquery name="local.getInstanceSettings" datasource="#application.dsn.membercentral.dsn#">
			select top 1 ai.applicationInstanceName, ai.applicationInstanceID, ai.siteResourceID,  
				COALESCE(ai.settingsXML,ats.settingsXML,at.settingsXML) as settingsXML,
				s.siteID, s.siteCode, o.orgID, o.orgcode
			from dbo.cms_applicationInstances as ai
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID
			inner join sites s on ai.siteID = s.siteID
			inner join organizations o on s.orgID = o.orgID
			left outer join dbo.cms_applicationTypeSettings as ats on ats.applicationTypeID = at.applicationTypeID and ats.siteID = ai.siteID
			where ai.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.appInstanceID#">
		</cfquery>
		
		<cfloop index="local.thisField" list="#local.getInstanceSettings.columnList#">
			<cfset local.strInstanceSettings[local.thisField] = local.getInstanceSettings[local.thisField]>
		</cfloop>
		<cfset local.strInstanceSettings.settingsXML = XMLParse(local.strInstanceSettings.settingsXML)>
		
		<cfreturn local.strInstanceSettings>
	</cffunction>
	
	<cffunction name="getWidgetInstanceSettings" access="public" returntype="struct">
		<cfargument name="applicationWidgetInstanceID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.strInstanceSettings=structNew()>

		<cfquery name="local.getInstanceSettings" datasource="#application.dsn.membercentral.dsn#">
			select awi.applicationWidgetInstanceName, awi.applicationWidgetInstanceID, awi.siteResourceID, 
				isnull(awi.settingsXML,awt.defaultSettingsXML) as settingsXML,
				ai.siteresourceID as applicationInstanceSiteResourceID, ai.applicationInstanceID, ai.siteID,
				s.sitecode, o.orgcode, o.orgID
			from cms_applicationWidgetInstances awi
			inner join cms_applicationInstances ai on ai.applicationInstanceID = awi.applicationInstanceID 
				and awi.applicationWidgetInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.applicationWidgetInstanceID#">
			inner join cms_applicationWidgetTypes awt on awi.applicationWidgetTypeID = awt.applicationWidgetTypeID
			inner join sites s on ai.siteID = s.siteID
			inner join organizations o on s.orgid = o.orgid
		</cfquery>
		
		<cfloop index="local.thisField" list="#local.getInstanceSettings.columnList#">
			<cfset local.strInstanceSettings[local.thisField] = local.getInstanceSettings[local.thisField]>
		</cfloop>

		<cftry>
			
			<cfset local.strInstanceSettings.settingsXML = XMLParse(local.getInstanceSettings.settingsXML)>
			
			<cfcatch type="any">
				<cfset application.objError.SendError(cfcatch=cfcatch,objectToDump=arguments)>
			</cfcatch>
		</cftry>
		
		
		<cfreturn local.strInstanceSettings>
	</cffunction>

	
	<cffunction name="getLocatorFieldsetID" access="public" output="no" returntype="query" hint="May return more than one">
		<cfargument name="siteresourceID" type="numeric" required="yes">
		<cfargument name="area" type="string" required="yes">
		<cfargument name="permissionMemberID" type="numeric" required="no" default="0">

		<cfset var qryFieldSet = "">
		
		<!--- get fieldset --->
		<cfquery name="qryFieldSet" datasource="#application.dsn.membercentral.dsn#">
			SELECT mfu.fieldsetID, mfu.useSiteResourceID, mfu.area, mfs.fieldsetName, mfs.nameFormat, mfs.showHelp
			FROM dbo.ams_memberFieldUsage as mfu
			inner join dbo.ams_memberfieldsets mfs on mfs.fieldsetID = mfu.fieldsetID
			where mfu.siteResourceID = <cfqueryparam value="#arguments.siteresourceID#" cfsqltype="CF_SQL_INTEGER">
			<cfif len(arguments.area)>
				and mfu.area = <cfqueryparam value="#arguments.area#" cfsqltype="CF_SQL_VARCHAR">
			</cfif>
			<cfif arguments.permissionMemberID gt 0>
				and dbo.fn_checkResourceRights(mfu.useSiteResourceID,93,<cfqueryparam value="#arguments.permissionMemberID#" cfsqltype="CF_SQL_INTEGER">,mfs.siteid) = 1
			</cfif>
			order by mfu.fieldsetorder, mfu.fieldsetid
		</cfquery>
	
		<cfreturn qryFieldSet>
	</cffunction>

	<cffunction name="qualifyAllLinks" access="public" output="no" returntype="string">
		<cfargument name="content" type="string" required="true">
		<cfargument name="siteid" type="numeric" required="true">
		<cfargument name="qualURL" type="string" required="true">

		<cfscript>
			var local = {};
			local.hostnameRegex = getQualifyAllLinksRegex(siteID=arguments.siteID);
			local.relativeURLRexex = "(?:(<\s*(?:a|img)\b[^>]*?\b(?:(?:href|src))\s*=\s*[""']))/((?:[^""']*)(?:[""']))";
			local.newContent = arguments.content.rereplacenocase(local.hostnameRegex,"\1#arguments.qualURL#\2","all").replacenocase('/index.cfm','/','all');
			local.newContent = local.newContent.rereplacenocase(local.relativeURLRexex,"\1#arguments.qualURL#\2","all");
			local.newContent = local.newContent.rereplacenocase('([?&]|&amp;)mk=(?!\[\[memberkey\]\])[^&"'' ]*\s*(?=[&"'' ])','\1mk=[[memberkey]]','all');
			return local.newContent;
		</cfscript>
	</cffunction>

	<cffunction name="getQualifyAllLinksRegex" access="private" output="no" returntype="string">
		<cfargument name="siteid" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryHostnamesForRegex" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,1,0)#">
			SET XACT_ABORT, NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			BEGIN TRY
	
				declare @siteID int, @orgcode varchar(10), @sitecode varchar(10);
				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select @orgcode = o.orgcode, @sitecode = s.sitecode
				from dbo.sites as s
				inner join dbo.organizations as o on o.orgID = s.orgID
				where s.siteID = @siteID;
			
				select hostNameList = replace(STRING_AGG(cast(hostname as varchar(max)),'|'),'.','\.')
				from (
					select hostname
					from dbo.sitehostnames
					where siteid = @siteID
						union
					select @sitecode + '.(?:[A-Za-z0-9]+).(?:membercentral|mcinternal).com'
						union
					select @orgcode + '.' + @sitecode + '.(?:[A-Za-z0-9]+).(?:membercentral|mcinternal).com'
						union
					select @sitecode + '.(?:membercentral|mccdn).org'
						union
					select @sitecode + '.(?:[A-Za-z0-9]+).(?:membercentral|mccdn).org'
				) as tmp;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.regex = "(?:(<\s*(?:a|img)\b[^>]*?\b(?:(?:href|src))\s*=\s*[""'])(?:/|https?://(?:#local.qryHostnamesForRegex.hostnamelist#)))\b(?:$|/)((?:[^""']*)(?:[""']))">
	
		<cfreturn local.regEx>
	</cffunction>


</cfcomponent>